import React, { useEffect, useRef } from 'react';
import Navigation from './components/Navigation';
import Hero from './components/Hero';
import Tools from './components/Tools';
import About from './components/About';
import Projects from './components/Projects';
import Experience from './components/Experience';
import Education from './components/Education';
import Expertise from './components/Expertise';
import Contact from './components/Contact';

function App() {
  const cursorRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const cursor = cursorRef.current;
    if (!cursor) return;

    const handleMouseMove = (e: MouseEvent) => {
      cursor.style.left = `${e.clientX}px`;
      cursor.style.top = `${e.clientY}px`;
    };

    const handleMouseEnter = () => {
      cursor.classList.add('hover');
    };

    const handleMouseLeave = () => {
      cursor.classList.remove('hover');
    };

    // Add mouse move listener
    document.addEventListener('mousemove', handleMouseMove);

    // Add hover effects for interactive elements
    const interactiveElements = document.querySelectorAll('button, a, input, textarea, select, [role="button"]');

    interactiveElements.forEach(element => {
      element.addEventListener('mouseenter', handleMouseEnter);
      element.addEventListener('mouseleave', handleMouseLeave);
    });

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      interactiveElements.forEach(element => {
        element.removeEventListener('mouseenter', handleMouseEnter);
        element.removeEventListener('mouseleave', handleMouseLeave);
      });
    };
  }, []);

  return (
    <div className="min-h-screen bg-cyberpunk text-white">
      {/* Custom cursor */}
      <div ref={cursorRef} className="custom-cursor"></div>

      <Navigation />
      <Hero />
      <Tools />
      <About />
      <Projects />
      <Experience />
      <Education />
      <Expertise />
      <Contact />

      {/* Footer */}
      <footer className="bg-black/80 border-t border-subtle py-6">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <div className="font-vt323 text-accent-blue text-lg mb-2">ALEX_NOVA.exe</div>
          <div className="font-mono text-sm text-secondary">
            © 2024 All rights reserved. Built with React & TypeScript.
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;