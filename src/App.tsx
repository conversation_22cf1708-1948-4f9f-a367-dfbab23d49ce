import React from 'react';
import Navigation from './components/Navigation';
import Hero from './components/Hero';
import Tools from './components/Tools';
import About from './components/About';
import Projects from './components/Projects';
import Experience from './components/Experience';
import Education from './components/Education';
import Expertise from './components/Expertise';
import Contact from './components/Contact';

function App() {
  return (
    <div className="min-h-screen bg-cyberpunk text-white">

      <Navigation />
      <Hero />
      <Tools />
      <About />
      <Projects />
      <Experience />
      <Education />
      <Expertise />
      <Contact />

      {/* Footer */}
      <footer className="bg-black/60 border-t border-accent-blue/10 py-12">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <div className="font-vt323 text-accent-blue text-lg mb-3 opacity-80">ALEX_NOVA.exe</div>
          <div className="font-mono text-sm text-muted">
            © 2024 All rights reserved. Built with React & TypeScript.
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;