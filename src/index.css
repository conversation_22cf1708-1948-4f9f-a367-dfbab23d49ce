@import url('https://fonts.googleapis.com/css2?family=VT323:wght@400&family=IBM+Plex+Mono:wght@400;500;600&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Properties */
:root {
  --neon-cyan: #64b5f6;
  --neon-magenta: #e91e63;
  --neon-purple: #7c4dff;
  --accent-blue: #2196f3;
  --accent-teal: #26a69a;
  --accent-orange: #ff7043;
  --dark-bg: #0a0a0f;
  --darker-bg: #050508;
  --card-bg: rgba(15, 15, 20, 0.8);
  --border-subtle: rgba(100, 181, 246, 0.15);
  --text-primary: #ffffff;
  --text-secondary: #b0bec5;
  --text-muted: #78909c;
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'IBM Plex Mono', monospace;
  background: var(--dark-bg);
  color: white;
  overflow-x: hidden;
  cursor: none;
}

/* Custom Cursor */
.custom-cursor {
  position: fixed;
  width: 16px;
  height: 16px;
  background: var(--accent-blue);
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: normal;
  transition: all 0.15s ease;
  border-radius: 50%;
  border: 2px solid rgba(100, 181, 246, 0.6);
  box-shadow: 0 0 8px rgba(100, 181, 246, 0.4);
  transform: translate(-50%, -50%);
}

.custom-cursor.hover {
  width: 24px;
  height: 24px;
  background: rgba(100, 181, 246, 0.2);
  border-color: var(--accent-blue);
  box-shadow: 0 0 12px rgba(100, 181, 246, 0.6);
}

/* Font Classes */
.font-vt323 {
  font-family: 'VT323', monospace;
}

/* Background */
.bg-cyberpunk {
  background:
    radial-gradient(circle at 20% 80%, rgba(100, 181, 246, 0.08) 0%, transparent 60%),
    radial-gradient(circle at 80% 20%, rgba(124, 77, 255, 0.06) 0%, transparent 60%),
    radial-gradient(circle at 40% 40%, rgba(38, 166, 154, 0.05) 0%, transparent 60%),
    linear-gradient(135deg, #0a0a0f 0%, #0f1419 100%);
  min-height: 100vh;
}

/* Grid Background */
.grid-bg {
  background-image:
    linear-gradient(rgba(100, 181, 246, 0.06) 1px, transparent 1px),
    linear-gradient(90deg, rgba(100, 181, 246, 0.06) 1px, transparent 1px);
  background-size: 60px 60px;
  width: 100%;
  height: 100%;
}

/* Subtle Glitch Effects */
.glitch-text {
  position: relative;
  animation: glitch 4s infinite;
}

.glitch-text::before,
.glitch-text::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.7;
}

.glitch-text::before {
  animation: glitch-1 1s infinite;
  color: var(--accent-blue);
  z-index: -1;
}

.glitch-text::after {
  animation: glitch-2 1s infinite;
  color: var(--accent-teal);
  z-index: -2;
}

@keyframes glitch {
  0%, 95% { transform: translate(0); }
  96% { transform: translate(-1px, 1px); }
  97% { transform: translate(1px, -1px); }
  98% { transform: translate(-1px, -1px); }
  99% { transform: translate(1px, 1px); }
  100% { transform: translate(0); }
}

@keyframes glitch-1 {
  0%, 95% { transform: translate(0); opacity: 0; }
  96% { transform: translate(-0.5px, 0.5px); opacity: 0.7; }
  97% { transform: translate(0.5px, -0.5px); opacity: 0.7; }
  98% { transform: translate(-0.5px, -0.5px); opacity: 0.7; }
  99% { transform: translate(0.5px, 0.5px); opacity: 0.7; }
  100% { transform: translate(0); opacity: 0; }
}

@keyframes glitch-2 {
  0%, 95% { transform: translate(0); opacity: 0; }
  96% { transform: translate(0.5px, -0.5px); opacity: 0.5; }
  97% { transform: translate(-0.5px, 0.5px); opacity: 0.5; }
  98% { transform: translate(0.5px, 0.5px); opacity: 0.5; }
  99% { transform: translate(-0.5px, -0.5px); opacity: 0.5; }
  100% { transform: translate(0); opacity: 0; }
}

/* Subtle Glow Effects */
.hover\:glow:hover {
  text-shadow: 0 0 8px rgba(100, 181, 246, 0.6);
}

.shadow-neon {
  box-shadow:
    0 0 4px rgba(100, 181, 246, 0.3),
    0 0 8px rgba(100, 181, 246, 0.2),
    0 0 12px rgba(100, 181, 246, 0.1);
}

.shadow-neon-lg {
  box-shadow:
    0 0 6px rgba(124, 77, 255, 0.4),
    0 0 12px rgba(124, 77, 255, 0.3),
    0 0 18px rgba(124, 77, 255, 0.2);
}

/* Terminal Container */
.terminal-container {
  position: relative;
}

.terminal-container::before {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(45deg, var(--accent-blue), var(--accent-teal), var(--neon-purple));
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.terminal-container:hover::before {
  opacity: 0.15;
}

/* Blink Animation */
.blink {
  animation: blink 2s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Modern Color Palette */
.text-magenta-400 {
  color: var(--neon-magenta);
}

.text-magenta-500 {
  color: #d81b60;
}

.bg-magenta-600 {
  background-color: #ad1457;
}

.bg-magenta-500 {
  background-color: var(--neon-magenta);
}

.hover\:bg-magenta-500:hover {
  background-color: #d81b60;
}

.border-magenta-400 {
  border-color: var(--neon-magenta);
}

.border-magenta-400\/30 {
  border-color: rgba(233, 30, 99, 0.3);
}

/* Additional modern colors */
.text-accent-blue {
  color: var(--accent-blue);
}

.text-accent-teal {
  color: var(--accent-teal);
}

.text-accent-orange {
  color: var(--accent-orange);
}

.bg-card {
  background-color: var(--card-bg);
}

.border-subtle {
  border-color: var(--border-subtle);
}

.text-primary {
  color: var(--text-primary);
}

.text-secondary {
  color: var(--text-secondary);
}

.text-muted {
  color: var(--text-muted);
}

/* Responsive Design */
@media (max-width: 768px) {
  .custom-cursor {
    display: none;
  }
  
  body {
    cursor: auto;
  }
  
  .font-vt323 {
    letter-spacing: 0.05em;
  }
}

/* Form Styling */
input:focus,
textarea:focus,
select:focus {
  box-shadow: 0 0 0 2px rgba(100, 181, 246, 0.4);
  outline: none;
}

/* Smooth Animations */
* {
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Modern Glass Effects */
.glass-card {
  background: var(--card-bg);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid var(--border-subtle);
  box-shadow:
    0 4px 16px 0 rgba(0, 0, 0, 0.3),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.05);
}

.glass-card:hover {
  background: rgba(15, 15, 20, 0.9);
  border: 1px solid rgba(100, 181, 246, 0.25);
  box-shadow:
    0 6px 20px 0 rgba(100, 181, 246, 0.1),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.08);
}

/* Glitch Container */
.glitch-container {
  position: relative;
}

.glitch-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    linear-gradient(90deg, transparent 24%, rgba(100, 181, 246, 0.02) 25%, rgba(100, 181, 246, 0.02) 26%, transparent 27%, transparent 74%, rgba(38, 166, 154, 0.02) 75%, rgba(38, 166, 154, 0.02) 76%, transparent 77%, transparent),
    linear-gradient(90deg, transparent 24%, rgba(100, 181, 246, 0.02) 25%, rgba(100, 181, 246, 0.02) 26%, transparent 27%, transparent 74%, rgba(38, 166, 154, 0.02) 75%, rgba(38, 166, 154, 0.02) 76%, transparent 77%, transparent);
  background-size: 75px 25px, 75px 25px;
  background-position: 0 0, 37px 12px;
  pointer-events: none;
  opacity: 0;
  animation: glitch-bg 12s infinite;
}

@keyframes glitch-bg {
  0% { opacity: 0; }
  3% { opacity: 1; }
  6% { opacity: 0; }
  9% { opacity: 1; }
  12% { opacity: 0; }
  100% { opacity: 0; }
}

/* Compact Spacing Utilities */
.section-compact {
  padding: 4rem 1rem;
}

.section-compact-sm {
  padding: 3rem 1rem;
}

.card-compact {
  padding: 1.5rem;
}

.card-compact-sm {
  padding: 1rem;
}

/* Typography Improvements */
.text-hierarchy-1 {
  font-size: 3rem;
  line-height: 1.1;
  font-weight: 400;
  letter-spacing: 0.02em;
}

.text-hierarchy-2 {
  font-size: 2rem;
  line-height: 1.2;
  font-weight: 400;
  letter-spacing: 0.01em;
}

.text-hierarchy-3 {
  font-size: 1.25rem;
  line-height: 1.4;
  font-weight: 500;
}

.text-body {
  font-size: 0.95rem;
  line-height: 1.6;
  color: var(--text-secondary);
}

.text-caption {
  font-size: 0.85rem;
  line-height: 1.5;
  color: var(--text-muted);
}

/* Improved Contrast */
.high-contrast {
  color: var(--text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.medium-contrast {
  color: var(--text-secondary);
}

.low-contrast {
  color: var(--text-muted);
}