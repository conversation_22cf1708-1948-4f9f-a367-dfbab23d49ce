@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=VT323:wght@400&family=IBM+Plex+Mono:wght@400;500;600&display=swap');

/* Custom Properties */
:root {
  --neon-cyan: #00ffff;
  --neon-magenta: #ff00ff;
  --neon-purple: #8a2be2;
  --dark-bg: #0a0a0f;
  --grid-color: rgba(138, 43, 226, 0.1);
}

/* Base Styles */
* {
  box-sizing: border-box;
}

body {
  font-family: 'IBM Plex Mono', monospace;
  background: var(--dark-bg);
  color: white;
  overflow-x: hidden;
  cursor: none;
}

/* Custom Cursor */
.custom-cursor {
  position: fixed;
  width: 12px;
  height: 12px;
  background: var(--neon-cyan);
  pointer-events: none;
  z-index: 9999;
  mix-blend-mode: difference;
  transition: all 0.1s ease;
  border-radius: 2px;
}

/* Font Classes */
.font-vt323 {
  font-family: 'VT323', monospace;
}

/* Background */
.bg-cyberpunk {
  background: 
    radial-gradient(circle at 20% 80%, rgba(138, 43, 226, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 0, 255, 0.2) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
    linear-gradient(135deg, #0a0a0f 0%, #1a0b2e 100%);
  min-height: 100vh;
}

/* Grid Background */
.grid-bg {
  background-image: 
    linear-gradient(rgba(138, 43, 226, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(138, 43, 226, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  width: 100%;
  height: 100%;
}

/* Glitch Effects */
.glitch-text {
  position: relative;
  animation: glitch 2s infinite;
}

.glitch-text::before,
.glitch-text::after {
  content: attr(data-text);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.glitch-text::before {
  animation: glitch-1 0.5s infinite;
  color: var(--neon-cyan);
  z-index: -1;
}

.glitch-text::after {
  animation: glitch-2 0.5s infinite;
  color: var(--neon-magenta);
  z-index: -2;
}

@keyframes glitch {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-2px, 2px); }
  40% { transform: translate(-2px, -2px); }
  60% { transform: translate(2px, 2px); }
  80% { transform: translate(2px, -2px); }
}

@keyframes glitch-1 {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(-1px, 1px); }
  40% { transform: translate(-1px, -1px); }
  60% { transform: translate(1px, 1px); }
  80% { transform: translate(1px, -1px); }
}

@keyframes glitch-2 {
  0%, 100% { transform: translate(0); }
  20% { transform: translate(1px, -1px); }
  40% { transform: translate(1px, 1px); }
  60% { transform: translate(-1px, -1px); }
  80% { transform: translate(-1px, 1px); }
}

/* Neon Glow Effects */
.hover\:glow:hover {
  text-shadow: 0 0 10px var(--neon-cyan);
}

.shadow-neon {
  box-shadow: 
    0 0 5px var(--neon-cyan),
    0 0 10px var(--neon-cyan),
    0 0 15px var(--neon-cyan);
}

.shadow-neon-lg {
  box-shadow: 
    0 0 10px var(--neon-magenta),
    0 0 20px var(--neon-magenta),
    0 0 30px var(--neon-magenta);
}

/* Terminal Container */
.terminal-container {
  position: relative;
}

.terminal-container::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--neon-cyan), var(--neon-magenta), var(--neon-purple));
  border-radius: inherit;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.terminal-container:hover::before {
  opacity: 0.3;
}

/* Blink Animation */
.blink {
  animation: blink 2s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* Custom Colors */
.text-magenta-400 {
  color: #ff4d9f;
}

.text-magenta-500 {
  color: #ff0066;
}

.bg-magenta-600 {
  background-color: #cc0052;
}

.bg-magenta-500 {
  background-color: #ff0066;
}

.hover\:bg-magenta-500:hover {
  background-color: #ff0066;
}

.border-magenta-400 {
  border-color: #ff4d9f;
}

.border-magenta-400\/30 {
  border-color: rgba(255, 77, 159, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .custom-cursor {
    display: none;
  }
  
  body {
    cursor: auto;
  }
  
  .font-vt323 {
    letter-spacing: 0.05em;
  }
}

/* Form Styling */
input:focus,
textarea:focus,
select:focus {
  box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.3);
}

/* Smooth Animations */
* {
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Glassmorphism Effects */
.glass-card {
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 
    0 8px 32px 0 rgba(31, 38, 135, 0.37),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
}

.glass-card:hover {
  background: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(0, 255, 255, 0.3);
  box-shadow: 
    0 8px 32px 0 rgba(0, 255, 255, 0.2),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.15);
}

/* Glitch Container */
.glitch-container {
  position: relative;
}

.glitch-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    linear-gradient(90deg, transparent 24%, rgba(255, 0, 255, 0.03) 25%, rgba(255, 0, 255, 0.03) 26%, transparent 27%, transparent 74%, rgba(0, 255, 255, 0.03) 75%, rgba(0, 255, 255, 0.03) 76%, transparent 77%, transparent),
    linear-gradient(90deg, transparent 24%, rgba(255, 0, 255, 0.03) 25%, rgba(255, 0, 255, 0.03) 26%, transparent 27%, transparent 74%, rgba(0, 255, 255, 0.03) 75%, rgba(0, 255, 255, 0.03) 76%, transparent 77%, transparent);
  background-size: 75px 25px, 75px 25px;
  background-position: 0 0, 37px 12px;
  pointer-events: none;
  opacity: 0;
  animation: glitch-bg 8s infinite;
}

@keyframes glitch-bg {
  0% { opacity: 0; }
  5% { opacity: 1; }
  10% { opacity: 0; }
  15% { opacity: 1; }
  20% { opacity: 0; }
  100% { opacity: 0; }
}