import React from 'react';
import { ArrowRight } from 'lucide-react';

const Hero: React.FC = () => {
  return (
    <section id="hero" className="min-h-screen flex items-center justify-center section-minimal pt-24 relative overflow-hidden">
      {/* Background Grid */}
      <div className="absolute inset-0 opacity-8">
        <div className="grid-bg"></div>
      </div>

      {/* Minimal Gradient Orb */}
      <div className="absolute top-1/3 right-1/3 w-96 h-96 bg-accent-blue/3 rounded-full blur-3xl"></div>

      <div className="max-w-7xl mx-auto grid lg:grid-cols-2 gap-16 items-center relative z-10">
        <div className="space-y-10">
          <div className="space-y-6">
            <div className="font-vt323 text-sm text-accent-blue tracking-wider opacity-80">[ SECTOR 7 ]</div>

            <h1 className="font-vt323 text-5xl lg:text-7xl text-primary leading-none tracking-wider glitch-text high-contrast">
              ALEX<br />
              <span className="text-accent-purple">WEB</span><br />
              <span className="text-accent-blue">DEVELOPER</span>
            </h1>

            <div className="font-vt323 text-lg text-secondary tracking-wider opacity-90">
              [ FULL STACK FREELANCER ]
            </div>
          </div>

          <div className="space-y-8">
            <p className="text-body leading-relaxed max-w-lg text-lg">
              Crafting <span className="text-accent-blue font-medium">digital experiences</span> with
              precision coding and <span className="text-accent-teal font-medium">modern aesthetics</span>.
              Transforming ideas into interactive realities.
            </p>

            <button className="group flex items-center space-x-3 bg-accent-purple hover:bg-purple-600 text-white px-6 py-3 font-vt323 text-base border border-accent-blue/30 hover:border-accent-blue/50 transition-all duration-300 hover:shadow-neon rounded-lg">
              <span>[ VIEW PORTFOLIO ]</span>
              <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </button>
          </div>
        </div>

        <div className="relative">
          <div className="glass-card border border-accent-blue/20 card-minimal rounded-xl hover:border-accent-blue/30 transition-colors duration-500">
            <div className="flex items-center space-x-3 mb-6">
              <div className="w-3 h-3 bg-red-400 rounded-full opacity-60"></div>
              <div className="w-3 h-3 bg-yellow-400 rounded-full opacity-60"></div>
              <div className="w-3 h-3 bg-green-400 rounded-full opacity-60"></div>
              <span className="font-vt323 text-accent-blue text-sm ml-4 opacity-80">NOVA_TERMINAL.exe</span>
            </div>

            <div className="font-mono text-sm space-y-3 leading-relaxed">
              <div className="text-green-400 opacity-80">$ whoami</div>
              <div className="text-secondary ml-2">alex_nova</div>
              <div className="text-green-400 opacity-80">$ cat skills.txt</div>
              <div className="text-accent-blue ml-2">→ React, TypeScript, Node.js</div>
              <div className="text-accent-blue ml-2">→ Python, PostgreSQL, MongoDB</div>
              <div className="text-accent-blue ml-2">→ Docker, AWS, Kubernetes</div>
              <div className="text-green-400 opacity-80">$ status</div>
              <div className="text-accent-teal blink ml-2">AVAILABLE_FOR_HIRE ●</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;