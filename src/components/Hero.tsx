import React from 'react';
import { ArrowRight } from 'lucide-react';

const Hero: React.FC = () => {
  return (
    <section id="hero" className="min-h-screen flex items-center justify-center px-4 pt-16 relative overflow-hidden">
      {/* Background Grid */}
      <div className="absolute inset-0 opacity-20">
        <div className="grid-bg"></div>
      </div>
      
      {/* Gradient Orbs */}
      <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-purple-600/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-magenta-600/15 rounded-full blur-3xl"></div>
      
      <div className="max-w-7xl mx-auto grid lg:grid-cols-2 gap-12 items-center relative z-10">
        <div className="space-y-8">
          <div className="space-y-4">
            <div className="font-vt323 text-sm text-cyan-400 tracking-wider">[ SECTOR 7 ]</div>
            
            <h1 className="font-vt323 text-6xl lg:text-8xl text-white leading-none tracking-wider glitch-text">
              ALEX<br />
              <span className="text-magenta-400">WEB</span><br />
              <span className="text-cyan-400">DEVELOPER</span>
            </h1>
            
            <div className="font-vt323 text-xl text-gray-300 tracking-wider">
              [ FULL STACK FREELANCER ]
            </div>
          </div>
          
          <div className="space-y-4">
            <p className="font-mono text-gray-300 leading-relaxed max-w-md">
              Crafting <span className="text-cyan-400 font-semibold">digital experiences</span> with 
              precision coding and <span className="text-magenta-400 font-semibold">cyberpunk aesthetics</span>.
              Transforming ideas into interactive realities.
            </p>
            
            <button className="group flex items-center space-x-3 bg-gradient-to-r from-magenta-600 to-purple-600 hover:from-magenta-500 hover:to-purple-500 text-white px-6 py-3 font-vt323 text-lg border border-cyan-400/50 hover:border-cyan-400 transition-all duration-300 hover:shadow-neon-lg">
              <span>[ PORTFOLIO ⮕ ]</span>
              <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </button>
          </div>
        </div>
        
        <div className="relative">
          <div className="terminal-container bg-black/60 border-2 border-cyan-400/50 p-6 rounded-lg backdrop-blur-sm hover:border-cyan-400 transition-colors duration-300">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="font-vt323 text-cyan-400 text-sm ml-4">NOVA_TERMINAL.exe</span>
            </div>
            
            <div className="font-mono text-sm space-y-2">
              <div className="text-green-400">$ whoami</div>
              <div className="text-gray-300">alex_nova</div>
              <div className="text-green-400">$ cat skills.txt</div>
              <div className="text-cyan-400">→ React, TypeScript, Node.js</div>
              <div className="text-cyan-400">→ Python, PostgreSQL, MongoDB</div>
              <div className="text-cyan-400">→ Docker, AWS, Kubernetes</div>
              <div className="text-green-400">$ status</div>
              <div className="text-magenta-400 blink">AVAILABLE_FOR_HIRE ●</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;