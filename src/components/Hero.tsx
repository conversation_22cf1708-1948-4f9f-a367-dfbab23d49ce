import React from 'react';
import { ArrowRight } from 'lucide-react';

const Hero: React.FC = () => {
  return (
    <section id="hero" className="min-h-screen flex items-center justify-center section-compact pt-20 relative overflow-hidden">
      {/* Background Grid */}
      <div className="absolute inset-0 opacity-15">
        <div className="grid-bg"></div>
      </div>

      {/* Gradient Orbs */}
      <div className="absolute top-1/4 left-1/4 w-48 h-48 bg-accent-blue/8 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-neon-purple/6 rounded-full blur-3xl"></div>

      <div className="max-w-7xl mx-auto grid lg:grid-cols-2 gap-8 items-center relative z-10">
        <div className="space-y-6">
          <div className="space-y-3">
            <div className="font-vt323 text-sm text-accent-blue tracking-wider">[ SECTOR 7 ]</div>

            <h1 className="font-vt323 text-5xl lg:text-7xl text-primary leading-none tracking-wider glitch-text high-contrast">
              ALEX<br />
              <span className="text-magenta-400">WEB</span><br />
              <span className="text-accent-blue">DEVELOPER</span>
            </h1>

            <div className="font-vt323 text-lg text-secondary tracking-wider">
              [ FULL STACK FREELANCER ]
            </div>
          </div>

          <div className="space-y-4">
            <p className="text-body leading-relaxed max-w-lg">
              Crafting <span className="text-accent-blue font-semibold">digital experiences</span> with
              precision coding and <span className="text-accent-teal font-semibold">modern aesthetics</span>.
              Transforming ideas into interactive realities.
            </p>

            <button className="group flex items-center space-x-3 bg-gradient-to-r from-magenta-500 to-neon-purple hover:from-magenta-600 hover:to-purple-600 text-white px-5 py-2.5 font-vt323 text-base border border-accent-blue/40 hover:border-accent-blue transition-all duration-300 hover:shadow-neon-lg rounded">
              <span>[ PORTFOLIO ⮕ ]</span>
              <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </button>
          </div>
        </div>

        <div className="relative">
          <div className="terminal-container glass-card border border-accent-blue/30 card-compact rounded-lg backdrop-blur-md hover:border-accent-blue/50 transition-colors duration-300">
            <div className="flex items-center space-x-2 mb-3">
              <div className="w-2.5 h-2.5 bg-red-500 rounded-full"></div>
              <div className="w-2.5 h-2.5 bg-yellow-500 rounded-full"></div>
              <div className="w-2.5 h-2.5 bg-green-500 rounded-full"></div>
              <span className="font-vt323 text-accent-blue text-sm ml-3">NOVA_TERMINAL.exe</span>
            </div>

            <div className="font-mono text-sm space-y-1.5">
              <div className="text-green-400">$ whoami</div>
              <div className="text-secondary">alex_nova</div>
              <div className="text-green-400">$ cat skills.txt</div>
              <div className="text-accent-blue">→ React, TypeScript, Node.js</div>
              <div className="text-accent-blue">→ Python, PostgreSQL, MongoDB</div>
              <div className="text-accent-blue">→ Docker, AWS, Kubernetes</div>
              <div className="text-green-400">$ status</div>
              <div className="text-accent-teal blink">AVAILABLE_FOR_HIRE ●</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;