import React, { useState, useEffect } from 'react';
import { Clock, MapPin, Send } from 'lucide-react';

const Contact: React.FC = () => {
  const [currentTime, setCurrentTime] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    workType: '',
    message: ''
  });

  useEffect(() => {
    const updateTime = () => {
      const now = new Date();
      const timeString = now.toLocaleTimeString('en-US', { 
        hour12: false,
        timeZone: 'Europe/Bratislava'
      });
      setCurrentTime(timeString);
    };

    updateTime();
    const interval = setInterval(updateTime, 1000);
    return () => clearInterval(interval);
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    // Handle form submission logic here
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <section id="contact" className="py-20 px-4 relative">
      <div className="max-w-7xl mx-auto">
        {/* Status Bar */}
        <div className="glass-card bg-black/60 border border-cyan-400/30 p-4 rounded-lg mb-12 flex flex-wrap items-center justify-between backdrop-blur-md">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-cyan-400" />
              <span className="font-vt323 text-cyan-400">SLOVAKIA</span>
            </div>
            <div className="font-vt323 text-gray-300">|</div>
            <div className="font-vt323 text-accent-teal">REMOTE</div>
            <div className="font-vt323 text-gray-300">|</div>
            <div className="flex items-center space-x-2">
              <Clock className="w-4 h-4 text-purple-400" />
              <span className="font-vt323 text-purple-400">{currentTime}</span>
            </div>
          </div>
          <div className="font-vt323 text-green-400 blink">AVAILABLE_FOR_HIRE ●</div>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-start">
          {/* Contact Form */}
          <div className="space-y-8">
            <div>
              <div className="font-vt323 text-sm text-cyan-400 tracking-wider mb-4">[ INITIALIZE_CONNECTION ]</div>
              <h2 className="font-vt323 text-4xl lg:text-5xl text-white tracking-wider mb-6">
                LET'S WORK
                <br />
                <span className="text-accent-purple">TOGETHER</span>
              </h2>
              
              <p className="font-mono text-gray-300 text-lg">
                I am looking forward to know more about your project!
              </p>
            </div>

            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <label className="block font-vt323 text-cyan-400 text-sm mb-2">NAME</label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    className="w-full bg-black/60 border border-purple-500/50 rounded px-4 py-3 text-white font-mono focus:border-cyan-400 focus:outline-none transition-colors duration-300"
                    placeholder="Enter your name..."
                    required
                  />
                </div>
                
                <div>
                  <label className="block font-vt323 text-cyan-400 text-sm mb-2">EMAIL</label>
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="w-full bg-black/60 border border-purple-500/50 rounded px-4 py-3 text-white font-mono focus:border-cyan-400 focus:outline-none transition-colors duration-300"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>

              <div>
                <label className="block font-vt323 text-cyan-400 text-sm mb-2">TYPE OF WORK</label>
                <select
                  name="workType"
                  value={formData.workType}
                  onChange={handleInputChange}
                  className="w-full bg-black/60 border border-purple-500/50 rounded px-4 py-3 text-white font-mono focus:border-cyan-400 focus:outline-none transition-colors duration-300"
                  required
                >
                  <option value="">Select work type...</option>
                  <option value="webflow">Webflow Development</option>
                  <option value="bug-fix">Bug Fix</option>
                  <option value="integration">API Integration</option>
                  <option value="full-stack">Full Stack Development</option>
                  <option value="consultation">Technical Consultation</option>
                </select>
              </div>

              <div>
                <label className="block font-vt323 text-cyan-400 text-sm mb-2">MESSAGE</label>
                <textarea
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows={6}
                  className="w-full bg-black/60 border border-purple-500/50 rounded px-4 py-3 text-white font-mono focus:border-cyan-400 focus:outline-none transition-colors duration-300 resize-none"
                  placeholder="Tell me about your project..."
                  required
                />
              </div>

              <button
                type="submit"
                className="group flex items-center space-x-3 bg-gradient-to-r from-accent-purple to-accent-indigo hover:from-purple-500 hover:to-indigo-500 text-white px-8 py-4 font-vt323 text-lg border border-accent-blue/50 hover:border-accent-blue transition-all duration-300 hover:shadow-neon-lg w-full justify-center"
              >
                <Send className="w-5 h-5" />
                <span>[ SEND_MESSAGE ]</span>
              </button>
            </form>
          </div>

          {/* Cyberpunk Avatar
          <div className="relative">
            <div className="glass-card bg-gradient-to-br from-purple-900/30 to-black/50 border-2 border-purple-500/50 rounded-lg p-8 backdrop-blur-md">
              <div className="text-center space-y-6">
                <div className="w-48 h-48 mx-auto bg-gradient-to-br from-cyan-400/20 to-magenta-400/20 rounded-lg border border-cyan-400/50 flex items-center justify-center">
                  <div className="text-6xl">🤖</div>
                </div>
                
                <div className="space-y-2">
                  <div className="font-vt323 text-xl text-cyan-400">ALEX_NOVA.AI</div>
                  <div className="font-mono text-sm text-gray-300">Neural Network Developer</div>
                  <div className="font-vt323 text-green-400 blink">● ONLINE</div>
                </div>

                <div className="space-y-2 font-mono text-sm">
                  <div className="text-gray-300">Response Time: <span className="text-cyan-400">&lt; 24hrs</span></div>
                  <div className="text-gray-300">Timezone: <span className="text-magenta-400">UTC+1</span></div>
                  <div className="text-gray-300">Languages: <span className="text-purple-400">EN, SK</span></div>
                </div>
              </div>
            </div>
          </div> */}
        </div>
      </div>
    </section>
  );
};

export default Contact;