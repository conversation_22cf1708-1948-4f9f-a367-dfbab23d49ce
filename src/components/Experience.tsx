import React from 'react';
import { Calendar, MapPin, Briefcase } from 'lucide-react';

const Experience: React.FC = () => {
  const experiences = [
    {
      title: 'Senior Full Stack Developer',
      company: 'CyberTech Solutions',
      location: 'Remote',
      period: '2023 - Present',
      type: 'Full-time',
      description: 'Leading development of enterprise-grade web applications with focus on cybersecurity and blockchain integration.',
      achievements: [
        'Architected microservices handling 1M+ daily requests',
        'Reduced deployment time by 70% using Docker & Kubernetes',
        'Led team of 5 developers on critical client projects'
      ],
      tech: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'Docker']
    },
    {
      title: 'Freelance Web Developer',
      company: 'Independent',
      location: 'Slovakia',
      period: '2021 - 2023',
      type: 'Freelance',
      description: 'Specialized in creating high-performance web applications and custom integrations for various clients.',
      achievements: [
        'Delivered 50+ successful projects',
        'Maintained 99.8% client satisfaction rate',
        'Generated $200K+ in revenue'
      ],
      tech: ['Webflow', 'React', 'Python', 'MongoDB', 'GSAP']
    },
    {
      title: 'Frontend Developer',
      company: 'Digital Innovations Ltd',
      location: 'Bratislava, SK',
      period: '2020 - 2021',
      type: 'Full-time',
      description: 'Developed responsive web interfaces and implemented modern design systems for B2B applications.',
      achievements: [
        'Improved page load speeds by 60%',
        'Implemented design system used across 10+ products',
        'Mentored 3 junior developers'
      ],
      tech: ['Vue.js', 'TypeScript', 'SCSS', 'Figma', 'Jest']
    }
  ];

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Full-time': return 'text-green-400 border-green-400/50';
      case 'Freelance': return 'text-cyan-400 border-cyan-400/50';
      case 'Contract': return 'text-yellow-400 border-yellow-400/50';
      default: return 'text-gray-400 border-gray-400/50';
    }
  };

  return (
    <section id="experience" className="py-20 px-4 relative">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <div className="font-vt323 text-sm text-cyan-400 tracking-wider mb-4">[ WORK_HISTORY ]</div>
          <h2 className="font-vt323 text-4xl lg:text-5xl text-white tracking-wider">
            PROFESSIONAL
            <br />
            <span className="text-magenta-400 glitch-text">EXPERIENCE</span>
          </h2>
        </div>

        <div className="space-y-8">
          {experiences.map((exp, index) => (
            <div
              key={index}
              className="group glass-card bg-black/60 border border-purple-500/30 rounded-lg p-8 hover:border-cyan-400/50 transition-all duration-300 backdrop-blur-md"
            >
              <div className="grid lg:grid-cols-3 gap-6">
                <div className="lg:col-span-2">
                  <div className="flex flex-wrap items-start justify-between mb-4">
                    <div>
                      <h3 className="font-vt323 text-2xl text-white mb-2">{exp.title}</h3>
                      <div className="flex items-center space-x-4 text-cyan-400 mb-2">
                        <div className="flex items-center space-x-2">
                          <Briefcase className="w-4 h-4" />
                          <span className="font-mono text-sm">{exp.company}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <MapPin className="w-4 h-4" />
                          <span className="font-mono text-sm">{exp.location}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2 text-gray-300">
                          <Calendar className="w-4 h-4" />
                          <span className="font-mono text-sm">{exp.period}</span>
                        </div>
                        <div className={`font-vt323 text-xs px-2 py-1 border rounded ${getTypeColor(exp.type)}`}>
                          {exp.type}
                        </div>
                      </div>
                    </div>
                  </div>

                  <p className="font-mono text-gray-300 text-sm leading-relaxed mb-4">
                    {exp.description}
                  </p>

                  <div className="space-y-2 mb-4">
                    <div className="font-vt323 text-magenta-400 text-sm">KEY_ACHIEVEMENTS:</div>
                    <ul className="space-y-1">
                      {exp.achievements.map((achievement, achIndex) => (
                        <li key={achIndex} className="flex items-start space-x-2 font-mono text-sm text-gray-300">
                          <span className="text-cyan-400 mt-1">→</span>
                          <span>{achievement}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <div className="font-vt323 text-purple-400 text-sm mb-3">TECH_STACK:</div>
                    <div className="flex flex-wrap gap-2">
                      {exp.tech.map((tech, techIndex) => (
                        <span
                          key={techIndex}
                          className="font-vt323 text-xs bg-purple-900/50 text-purple-300 px-2 py-1 rounded border border-purple-500/30"
                        >
                          {tech}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Timeline connector */}
              {index < experiences.length - 1 && (
                <div className="absolute left-8 bottom-0 w-0.5 h-8 bg-gradient-to-b from-purple-500/50 to-transparent"></div>
              )}
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Experience;