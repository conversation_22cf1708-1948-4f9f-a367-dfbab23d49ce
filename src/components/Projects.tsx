import React from 'react';
import { ExternalLink, Github, Zap, Globe, Database } from 'lucide-react';

const Projects: React.FC = () => {
  const projects = [
    {
      title: 'CYBERPUNK DASHBOARD',
      description: 'Real-time analytics dashboard with cyberpunk aesthetics and advanced data visualization.',
      tech: ['React', 'TypeScript', 'D3.js', 'WebGL'],
      status: 'LIVE',
      image: '🖥️',
      links: {
        live: '#',
        github: '#'
      }
    },
    {
      title: 'NEURAL NETWORK API',
      description: 'Machine learning API for image recognition with custom neural network architecture.',
      tech: ['Python', 'TensorFlow', 'FastAPI', 'Docker'],
      status: 'BETA',
      image: '🧠',
      links: {
        live: '#',
        github: '#'
      }
    },
    {
      title: 'BLOCKCHAIN WALLET',
      description: 'Secure cryptocurrency wallet with multi-chain support and DeFi integration.',
      tech: ['Solidity', 'Web3.js', 'React', 'Node.js'],
      status: 'DEV',
      image: '⛓️',
      links: {
        live: '#',
        github: '#'
      }
    },
    {
      title: 'QUANTUM SIMULATOR',
      description: 'Quantum computing simulator with visual circuit builder and algorithm library.',
      tech: ['Python', 'Qiskit', 'Vue.js', 'WebAssembly'],
      status: 'LIVE',
      image: '⚛️',
      links: {
        live: '#',
        github: '#'
      }
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'LIVE': return 'text-green-400 border-green-400/50';
      case 'BETA': return 'text-yellow-400 border-yellow-400/50';
      case 'DEV': return 'text-cyan-400 border-cyan-400/50';
      default: return 'text-gray-400 border-gray-400/50';
    }
  };

  return (
    <section id="projects" className="py-20 px-4 relative">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <div className="font-vt323 text-sm text-cyan-400 tracking-wider mb-4">[ PROJECT_ARCHIVE ]</div>
          <h2 className="font-vt323 text-4xl lg:text-5xl text-white tracking-wider">
            DIGITAL
            <br />
            <span className="text-magenta-400 glitch-text">CREATIONS</span>
          </h2>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {projects.map((project, index) => (
            <div
              key={index}
              className="group glass-card bg-black/60 border border-purple-500/30 rounded-lg p-6 hover:border-cyan-400/50 transition-all duration-300 cursor-pointer hover:shadow-neon transform hover:scale-105 backdrop-blur-md"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="text-4xl">{project.image}</div>
                <div className={`font-vt323 text-xs px-2 py-1 border rounded ${getStatusColor(project.status)}`}>
                  {project.status}
                </div>
              </div>

              <h3 className="font-vt323 text-xl text-white mb-3">{project.title}</h3>
              <p className="font-mono text-sm text-gray-300 mb-4 leading-relaxed">
                {project.description}
              </p>

              <div className="flex flex-wrap gap-2 mb-4">
                {project.tech.map((tech, techIndex) => (
                  <span
                    key={techIndex}
                    className="font-vt323 text-xs bg-purple-900/50 text-purple-300 px-2 py-1 rounded border border-purple-500/30"
                  >
                    {tech}
                  </span>
                ))}
              </div>

              <div className="flex items-center space-x-4">
                <button className="flex items-center space-x-2 text-cyan-400 hover:text-white transition-colors">
                  <Globe className="w-4 h-4" />
                  <span className="font-vt323 text-sm">DEMO</span>
                </button>
                <button className="flex items-center space-x-2 text-magenta-400 hover:text-white transition-colors">
                  <Github className="w-4 h-4" />
                  <span className="font-vt323 text-sm">CODE</span>
                </button>
              </div>

              {/* Hover effect overlay */}
              <div className="absolute inset-0 rounded-lg border border-cyan-400/0 group-hover:border-cyan-400/30 transition-all duration-300 pointer-events-none"></div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <button className="group flex items-center space-x-3 bg-gradient-to-r from-purple-600 to-magenta-600 hover:from-purple-500 hover:to-magenta-500 text-white px-6 py-3 font-vt323 text-lg border border-cyan-400/50 hover:border-cyan-400 transition-all duration-300 hover:shadow-neon-lg mx-auto">
            <Database className="w-5 h-5" />
            <span>[ VIEW_ALL_PROJECTS ]</span>
          </button>
        </div>
      </div>
    </section>
  );
};

export default Projects;