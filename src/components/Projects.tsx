import React from 'react';
import { Globe, Database, Github } from 'lucide-react';

const Projects: React.FC = () => {
  const projects = [
    {
      title: 'AI AGENT - CODE REVIEWER',
      description: 'AI agent that connects to repository, parses codebase structure, and enables conversational code review via LLM chat interface.',
      tech: ['Python', 'LangChain', 'OpenAI', 'Git API'],
      status: 'LIVE',
      image: '🤖',
      links: {
        live: '#',
        github: 'https://github.com/PratikJH153'
      }
    },
    {
      title: 'ENTHEM - SOCIAL MEDIA APP',
      description: 'Platform enabling users to connect with like-minded peers nearby using Neo4j proximity-based graph clustering and hyper-personalized chat rooms.',
      tech: ['Flutter', 'Neo4j', 'Node.js', 'Firebase'],
      status: 'LIVE',
      image: '📱',
      links: {
        live: 'https://pratikjh.netlify.app',
        github: 'https://github.com/PratikJH153'
      }
    },
    {
      title: 'VIBEO - AI NIGHTLIFE DISCOVERY',
      description: 'AI-powered mobile app for personalized nightlife video discovery with real-time venue analysis and recommendation engine.',
      tech: ['Flutter', 'Python', 'GCP', 'OpenAI Vision', 'Pinecone'],
      status: 'BETA',
      image: '🎥',
      links: {
        live: 'https://www.vibeo.io',
        github: '#'
      }
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'LIVE': return 'text-green-400 border-green-400/50';
      case 'BETA': return 'text-yellow-400 border-yellow-400/50';
      case 'DEV': return 'text-cyan-400 border-cyan-400/50';
      default: return 'text-gray-400 border-gray-400/50';
    }
  };

  return (
    <section id="projects" className="py-20 px-4 relative">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <div className="font-vt323 text-sm text-cyan-400 tracking-wider mb-4">[ PROJECT_ARCHIVE ]</div>
          <h2 className="font-vt323 text-4xl lg:text-5xl text-white tracking-wider">
            DIGITAL
            <br />
            <span className="text-accent-purple glitch-text">CREATIONS</span>
          </h2>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {projects.map((project, index) => (
            <div
              key={index}
              className="group glass-card bg-black/60 border border-purple-500/30 rounded-lg p-6 hover:border-cyan-400/50 transition-all duration-300 cursor-pointer hover:shadow-neon transform hover:scale-105 backdrop-blur-md"
            >
              <div className="flex items-start justify-between mb-4">
                <div className="text-4xl">{project.image}</div>
                <div className={`font-vt323 text-xs px-2 py-1 border rounded ${getStatusColor(project.status)}`}>
                  {project.status}
                </div>
              </div>

              <h3 className="font-vt323 text-xl text-white mb-3">{project.title}</h3>
              <p className="font-mono text-sm text-gray-300 mb-4 leading-relaxed">
                {project.description}
              </p>

              <div className="flex flex-wrap gap-2 mb-4">
                {project.tech.map((tech, techIndex) => (
                  <span
                    key={techIndex}
                    className="font-vt323 text-xs bg-purple-900/50 text-purple-300 px-2 py-1 rounded border border-purple-500/30"
                  >
                    {tech}
                  </span>
                ))}
              </div>

              <div className="flex items-center space-x-4">
                <button className="flex items-center space-x-2 text-cyan-400 hover:text-white transition-colors">
                  <Globe className="w-4 h-4" />
                  <span className="font-vt323 text-sm">DEMO</span>
                </button>
                <button className="flex items-center space-x-2 text-accent-purple hover:text-white transition-colors">
                  <Github className="w-4 h-4" />
                  <span className="font-vt323 text-sm">CODE</span>
                </button>
              </div>

              {/* Hover effect overlay */}
              <div className="absolute inset-0 rounded-lg border border-cyan-400/0 group-hover:border-cyan-400/30 transition-all duration-300 pointer-events-none"></div>
            </div>
          ))}
        </div>

        <div className="mt-12 text-center">
          <button className="group flex items-center space-x-3 bg-gradient-to-r from-purple-600 to-accent-indigo hover:from-purple-500 hover:to-indigo-500 text-white px-6 py-3 font-vt323 text-lg border border-accent-blue/50 hover:border-accent-blue transition-all duration-300 hover:shadow-neon-lg mx-auto">
            <Database className="w-5 h-5" />
            <span>[ VIEW_ALL_PROJECTS ]</span>
          </button>
        </div>
      </div>
    </section>
  );
};

export default Projects;