import React from 'react';

const About: React.FC = () => {
  return (
    <section id="about" className="py-20 px-4 relative">
      <div className="max-w-7xl mx-auto">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <div className="space-y-4">
              <div className="font-vt323 text-2xl text-gray-300">
                &lt; <span className="text-accent-blue">seamless</span> /&gt; &lt; <span className="text-accent-teal">user-focused</span> /&gt;
              </div>

              <h2 className="font-vt323 text-4xl lg:text-5xl text-white leading-tight">
                DRIVEN BY <span className="text-accent-blue glitch-text">PRECISION</span>,<br />
                GUIDED BY <span className="text-accent-purple">INDUSTRY</span><br />
                <span className="text-accent-indigo">STANDARDS</span>
              </h2>
            </div>
            
            <div className="space-y-6 font-mono text-gray-300 leading-relaxed">
              <p>
                I transform <span className="text-accent-blue font-semibold">abstract ideas</span> into
                <span className="text-accent-teal font-semibold"> high-end</span> solutions, serving as
                a bridge to your business <span className="text-accent-purple font-semibold">success</span>.
              </p>
              
              <p>
                With expertise in full-stack development and a passion for 
                <span className="text-cyan-400 font-semibold"> cyberpunk aesthetics</span>, 
                I create digital experiences that push the boundaries of modern web development.
              </p>
              
              <div className="grid grid-cols-2 gap-4 mt-8">
                <div className="glass-card border border-accent-blue/30 p-4 rounded backdrop-blur-md">
                  <div className="font-vt323 text-accent-blue text-lg">150+</div>
                  <div className="text-sm">Projects Completed</div>
                </div>
                <div className="glass-card border border-accent-purple/30 p-4 rounded backdrop-blur-md">
                  <div className="font-vt323 text-accent-purple text-lg">99.8%</div>
                  <div className="text-sm">Client Satisfaction</div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="relative">
            <div className="glitch-container">
              <div className="glass-card bg-gradient-to-br from-purple-900/50 to-black/50 border-2 border-purple-500/50 rounded-lg p-8 backdrop-blur-md">
                <div className="space-y-4">
                  <div className="font-vt323 text-cyan-400 text-lg">CORE_PRINCIPLES.exe</div>
                  
                  <div className="space-y-3 font-mono text-sm">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-400 rounded-full blink"></div>
                      <span className="text-gray-300">Clean, maintainable code</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-400 rounded-full blink" style={{animationDelay: '0.5s'}}></div>
                      <span className="text-gray-300">Performance optimization</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-400 rounded-full blink" style={{animationDelay: '1s'}}></div>
                      <span className="text-gray-300">User experience focus</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-400 rounded-full blink" style={{animationDelay: '1.5s'}}></div>
                      <span className="text-gray-300">Cutting-edge technologies</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default About;