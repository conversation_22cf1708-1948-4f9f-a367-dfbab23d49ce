import React from 'react';
import { Shield, Award, Code, Database } from 'lucide-react';

const Expertise: React.FC = () => {
  const certifications = [
    {
      title: 'Advanced JavaScript',
      platform: 'Complete JavaScript Course: From Zero to Expert',
      date: 'Jan 2024',
      icon: Code,
      verified: true
    },
    {
      title: 'React Expert',
      platform: 'React - The Complete Guide',
      date: 'Feb 2024',
      icon: Code,
      verified: true
    },
    {
      title: 'Node.js Certification',
      platform: 'The Complete Node.js Developer Course',
      date: 'Mar 2024',
      icon: Database,
      verified: true
    },
    {
      title: 'AWS Solutions Architect',
      platform: 'Amazon Web Services',
      date: 'Apr 2024',
      icon: Shield,
      verified: true
    }
  ];

  return (
    <section id="expertise" className="py-20 px-4 relative">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <div className="font-vt323 text-sm text-cyan-400 tracking-wider mb-4">[ CREDENTIALS ]</div>
          <h2 className="font-vt323 text-4xl lg:text-5xl text-white tracking-wider">
            MY EXPERTISE
            <br />
                className="group glass-card bg-black/60 border border-purple-500/30 p-6 rounded-lg hover:border-cyan-400/50 transition-all duration-300 cursor-pointer hover:shadow-neon transform hover:scale-105 backdrop-blur-md"
          </h2>
        </div>
        
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {certifications.map((cert, index) => (
            <div
              key={index}
              className="group bg-black/60 border border-purple-500/30 p-6 rounded-lg hover:border-cyan-400/50 transition-all duration-300 cursor-pointer hover:shadow-neon transform hover:scale-105"
            >
              <div className="flex items-start justify-between mb-4">
                <cert.icon className="w-8 h-8 text-cyan-400" />
                {cert.verified && (
                  <div className="flex items-center space-x-1">
                    <Award className="w-4 h-4 text-green-400" />
                    <span className="font-vt323 text-xs text-green-400">VERIFIED</span>
                  </div>
                )}
              </div>
              
              <h3 className="font-vt323 text-lg text-white mb-2">{cert.title}</h3>
              <p className="font-mono text-sm text-gray-300 mb-3">{cert.platform}</p>
              <div className="font-vt323 text-sm text-cyan-400">{cert.date}</div>
              
              {/* Hover effect overlay */}
              <div className="absolute inset-0 rounded-lg border border-cyan-400/0 group-hover:border-cyan-400/30 transition-all duration-300 pointer-events-none"></div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Expertise;