import React from 'react';
import { GraduationCap, Award, BookOpen, Calendar } from 'lucide-react';

const Education: React.FC = () => {
  const education = [
    {
      degree: 'Master of Science in Computer Science',
      institution: 'Illinois Institute of Technology',
      location: 'Chicago, IL',
      period: '08/2023 - 05/2025',
      gpa: '3.6/4.0',
      specialization: 'Artificial Intelligence & Machine Learning',
      thesis: 'AI-Powered Video Analysis for Personalized Recommendations',
      achievements: [
        'Graduate Teaching Assistant for 3 Semesters',
        'Relevant Coursework: AI & ML, Software Engineering, Operating Systems',
        'Focus on Big Data and Algorithms'
      ]
    },
    {
      degree: 'Bachelor of Technology in Computer Science',
      institution: 'D.Y. Patil International University',
      location: 'Pune, India',
      period: '08/2019 - 06/2023',
      gpa: '3.55/4.0',
      specialization: 'Artificial Intelligence & Machine Learning',
      thesis: 'Machine Learning Applications in Real-time Systems',
      achievements: [
        'Specialized in AI/ML technologies',
        'Strong foundation in computer science fundamentals',
        'Active in coding competitions and hackathons'
      ]
    }
  ];

  const certifications = [
    {
      name: 'AWS Solutions Architect Professional',
      issuer: 'Amazon Web Services',
      date: '2024',
      credentialId: 'AWS-SAP-2024-001',
      icon: '☁️'
    },
    {
      name: 'Google Cloud Professional Developer',
      issuer: 'Google Cloud',
      date: '2023',
      credentialId: 'GCP-PD-2023-042',
      icon: '🌐'
    },
    {
      name: 'Certified Kubernetes Administrator',
      issuer: 'Cloud Native Computing Foundation',
      date: '2023',
      credentialId: 'CKA-2023-789',
      icon: '⚙️'
    },
    {
      name: 'MongoDB Certified Developer',
      issuer: 'MongoDB Inc.',
      date: '2022',
      credentialId: 'MDB-DEV-2022-156',
      icon: '🍃'
    }
  ];

  return (
    <section id="education" className="py-20 px-4 relative">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <div className="font-vt323 text-sm text-cyan-400 tracking-wider mb-4">[ KNOWLEDGE_BASE ]</div>
          <h2 className="font-vt323 text-4xl lg:text-5xl text-white tracking-wider">
            EDUCATION &
            <br />
            <span className="text-accent-purple glitch-text">CERTIFICATIONS</span>
          </h2>
        </div>

        {/* Education */}
        <div className="mb-16">
          <div className="font-vt323 text-2xl text-cyan-400 mb-8 flex items-center space-x-3">
            <GraduationCap className="w-6 h-6" />
            <span>ACADEMIC_BACKGROUND</span>
          </div>

          <div className="space-y-8">
            {education.map((edu, index) => (
              <div
                key={index}
                className="glass-card bg-black/60 border border-purple-500/30 rounded-lg p-8 hover:border-cyan-400/50 transition-all duration-300 backdrop-blur-md"
              >
                <div className="grid lg:grid-cols-3 gap-6">
                  <div className="lg:col-span-2">
                    <h3 className="font-vt323 text-2xl text-white mb-2">{edu.degree}</h3>
                    <div className="flex flex-wrap items-center space-x-4 text-cyan-400 mb-4">
                      <div className="flex items-center space-x-2">
                        <BookOpen className="w-4 h-4" />
                        <span className="font-mono text-sm">{edu.institution}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="w-4 h-4" />
                        <span className="font-mono text-sm">{edu.period}</span>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="font-mono text-sm text-gray-300">
                        <span className="text-accent-teal font-semibold">Specialization:</span> {edu.specialization}
                      </div>
                      <div className="font-mono text-sm text-gray-300">
                        <span className="text-purple-400 font-semibold">Thesis:</span> {edu.thesis}
                      </div>
                      <div className="font-mono text-sm text-gray-300">
                        <span className="text-cyan-400 font-semibold">GPA:</span> {edu.gpa}
                      </div>
                    </div>
                  </div>

                  <div>
                    <div className="font-vt323 text-green-400 text-sm mb-3">ACHIEVEMENTS:</div>
                    <ul className="space-y-2">
                      {edu.achievements.map((achievement, achIndex) => (
                        <li key={achIndex} className="flex items-start space-x-2 font-mono text-sm text-gray-300">
                          <Award className="w-4 h-4 text-yellow-400 mt-0.5 flex-shrink-0" />
                          <span>{achievement}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Certifications */}
        <div>
          <div className="font-vt323 text-2xl text-accent-purple mb-8 flex items-center space-x-3">
            <Award className="w-6 h-6" />
            <span>PROFESSIONAL_CERTIFICATIONS</span>
          </div>

          <div className="grid md:grid-cols-2 gap-6">
            {certifications.map((cert, index) => (
              <div
                key={index}
                className="glass-card bg-black/60 border border-purple-500/30 rounded-lg p-6 hover:border-cyan-400/50 transition-all duration-300 cursor-pointer hover:shadow-neon transform hover:scale-105 backdrop-blur-md"
              >
                <div className="flex items-start space-x-4">
                  <div className="text-3xl">{cert.icon}</div>
                  <div className="flex-1">
                    <h4 className="font-vt323 text-lg text-white mb-2">{cert.name}</h4>
                    <div className="space-y-1">
                      <div className="font-mono text-sm text-cyan-400">{cert.issuer}</div>
                      <div className="font-mono text-sm text-gray-300">Issued: {cert.date}</div>
                      <div className="font-vt323 text-xs text-purple-400">ID: {cert.credentialId}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-400 rounded-full blink"></div>
                    <span className="font-vt323 text-xs text-green-400">VERIFIED</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default Education;