import React from 'react';

const Tools: React.FC = () => {
  const tools = [
    'MAKE.COM', 'Z<PERSON>IER', 'CALENDLY', 'MEMBERSTACK', 'MAPBOX', 'GSAP',
    'WEBFLOW', 'REACT', 'NODEJS', 'DOCKER', 'AWS', 'MONGODB'
  ];

  return (
    <section id="tools" className="section-spacious-sm relative">
      <div className="max-w-6xl mx-auto">
        <div className="text-center spacing-relaxed">
          <div className="font-vt323 text-sm text-accent-blue tracking-wider mb-6 opacity-70">[ DEVELOPMENT STACK ]</div>
          <h2 className="font-vt323 text-4xl lg:text-5xl text-primary tracking-wider high-contrast mb-4">
            TOOLS &amp;
            <br />
            <span className="text-accent-teal">TECHNOLOGIES</span>
          </h2>
        </div>

        <div className="flex flex-wrap justify-center gap-4 max-w-4xl mx-auto">
          {tools.map((tool, index) => (
            <div
              key={tool}
              className="group"
              style={{ animationDelay: `${index * 30}ms` }}
            >
              <div className="bg-card border border-accent-blue/10 px-4 py-2 rounded-lg font-vt323 text-sm text-secondary hover:text-primary hover:border-accent-blue/25 transition-all duration-500 cursor-pointer hover:bg-accent-blue/5">
                {tool}
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-16 grid md:grid-cols-2 gap-8">
          <div className="glass-card bg-black/40 border border-purple-500/30 p-6 rounded-lg backdrop-blur-md">
            <h3 className="font-vt323 text-xl text-accent-purple mb-4">FRONTEND ARSENAL</h3>
            <div className="space-y-2 font-mono text-sm text-gray-300">
              <div>→ React, Vue.js, TypeScript</div>
              <div>→ Tailwind CSS, SCSS, Styled Components</div>
              <div>→ Three.js, GSAP, Framer Motion</div>
            </div>
          </div>
          
          <div className="glass-card bg-black/40 border border-purple-500/30 p-6 rounded-lg backdrop-blur-md">
            <h3 className="font-vt323 text-xl text-cyan-400 mb-4">BACKEND STACK</h3>
            <div className="space-y-2 font-mono text-sm text-gray-300">
              <div>→ Node.js, Python, PostgreSQL</div>
              <div>→ Redis, Docker, Kubernetes</div>
              <div>→ AWS, GCP, Serverless Functions</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Tools;