import React from 'react';

const Tools: React.FC = () => {
  const tools = [
    'MAKE.COM', 'Z<PERSON>IER', 'CALENDLY', 'MEMBERSTACK', 'MAPBOX', 'GSAP',
    'WEBFLOW', 'REACT', 'NODEJS', 'DOCKER', 'AWS', 'MONGODB'
  ];

  return (
    <section id="tools" className="section-compact-sm relative">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-8">
          <div className="font-vt323 text-sm text-accent-blue tracking-wider mb-3">[ TOOLS I USE ]</div>
          <h2 className="font-vt323 text-hierarchy-1 lg:text-hierarchy-2 text-primary tracking-wider high-contrast">
            DEVELOPMENT
            <br />
            <span className="text-accent-teal">TOOLKIT</span>
          </h2>
        </div>

        <div className="flex flex-wrap justify-center gap-3">
          {tools.map((tool, index) => (
            <div
              key={tool}
              className="group relative"
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <div className="glass-card border border-subtle px-3 py-1.5 rounded-full font-vt323 text-sm text-secondary hover:text-primary hover:border-accent-blue/40 transition-all duration-300 cursor-pointer hover:shadow-neon transform hover:scale-105">
                {tool}
              </div>

              {/* Hover effect */}
              <div className="absolute inset-0 rounded-full border border-accent-blue/0 group-hover:border-accent-blue/30 transition-all duration-300 pointer-events-none"></div>
            </div>
          ))}
        </div>
        
        <div className="mt-16 grid md:grid-cols-2 gap-8">
          <div className="glass-card bg-black/40 border border-purple-500/30 p-6 rounded-lg backdrop-blur-md">
            <h3 className="font-vt323 text-xl text-magenta-400 mb-4">FRONTEND ARSENAL</h3>
            <div className="space-y-2 font-mono text-sm text-gray-300">
              <div>→ React, Vue.js, TypeScript</div>
              <div>→ Tailwind CSS, SCSS, Styled Components</div>
              <div>→ Three.js, GSAP, Framer Motion</div>
            </div>
          </div>
          
          <div className="glass-card bg-black/40 border border-purple-500/30 p-6 rounded-lg backdrop-blur-md">
            <h3 className="font-vt323 text-xl text-cyan-400 mb-4">BACKEND STACK</h3>
            <div className="space-y-2 font-mono text-sm text-gray-300">
              <div>→ Node.js, Python, PostgreSQL</div>
              <div>→ Redis, Docker, Kubernetes</div>
              <div>→ AWS, GCP, Serverless Functions</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Tools;