import React from 'react';

const Tools: React.FC = () => {
  const tools = [
    'PYTHON', 'C++', 'TYPESCRIPT', 'JAVASCRIPT', 'DART', 'FLUTTER',
    'NODE.JS', 'NESTJS', '<PERSON>NGCHAIN', 'PANDAS', 'NUMPY', 'MONGOD<PERSON>',
    'MYSQL', 'NEO4J', 'REDIS', 'FIREBASE', 'DOCKER', 'GCP', 'N8N'
  ];

  return (
    <section id="tools" className="section-spacious-sm relative">
      <div className="max-w-6xl mx-auto">
        <div className="text-center spacing-relaxed">
          <div className="font-vt323 text-sm text-accent-blue tracking-wider mb-6 opacity-70">[ DEVELOPMENT STACK ]</div>
          <h2 className="font-vt323 text-4xl lg:text-5xl text-primary tracking-wider high-contrast mb-4">
            TECHNICAL
            <br />
            <span className="text-accent-teal">SKILLS</span>
          </h2>
        </div>

        <div className="flex flex-wrap justify-center gap-4 max-w-4xl mx-auto">
          {tools.map((tool, index) => (
            <div
              key={tool}
              className="group"
              style={{ animationDelay: `${index * 30}ms` }}
            >
              <div className="bg-card border border-accent-blue/10 px-4 py-2 rounded-lg font-vt323 text-sm text-secondary hover:text-primary hover:border-accent-blue/25 transition-all duration-500 cursor-pointer hover:bg-accent-blue/5">
                {tool}
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-16 grid md:grid-cols-2 gap-8">
          <div className="glass-card border border-accent-purple/30 card-minimal rounded-lg backdrop-blur-md">
            <h3 className="font-vt323 text-xl text-accent-purple mb-4">AI/ML & BACKEND</h3>
            <div className="space-y-2 font-mono text-sm text-secondary">
              <div>→ Python, LangChain, Pandas, NumPy</div>
              <div>→ Node.js, NestJS, TypeScript</div>
              <div>→ OpenAI Vision, Vector Databases</div>
            </div>
          </div>

          <div className="glass-card border border-accent-blue/30 card-minimal rounded-lg backdrop-blur-md">
            <h3 className="font-vt323 text-xl text-accent-blue mb-4">MOBILE & CLOUD</h3>
            <div className="space-y-2 font-mono text-sm text-secondary">
              <div>→ Flutter, Dart, Mobile Development</div>
              <div>→ Google Cloud Platform, Docker</div>
              <div>→ MongoDB, Neo4j, Redis, Firebase</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Tools;