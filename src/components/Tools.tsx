import React from 'react';

const Tools: React.FC = () => {
  const tools = [
    'MAKE.COM', 'Z<PERSON>IER', 'CALENDLY', 'MEMBERSTACK', 'MAPBOX', 'GSAP',
    'WEBFLOW', 'REACT', 'NODEJS', 'DOCKER', 'AWS', 'MONGODB'
  ];

  return (
    <section id="tools" className="py-20 px-4 relative">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <div className="font-vt323 text-sm text-cyan-400 tracking-wider mb-4">[ TOOLS I USE ]</div>
          <h2 className="font-vt323 text-4xl lg:text-5xl text-white tracking-wider">
            STUFF I USE
            <br />
            <span className="text-cyan-400">IN WEBFLOW</span>
          </h2>
        </div>
        
        <div className="flex flex-wrap justify-center gap-4">
          {tools.map((tool, index) => (
            <div
              key={tool}
              className="group relative"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="bg-black/60 border border-purple-500/50 px-4 py-2 rounded-full font-vt323 text-sm text-gray-300 hover:text-white hover:border-cyan-400 transition-all duration-300 cursor-pointer hover:shadow-neon transform hover:scale-105">
                {tool}
              </div>
              
              {/* Hover effect */}
              <div className="absolute inset-0 rounded-full border border-cyan-400/0 group-hover:border-cyan-400/50 transition-all duration-300 pointer-events-none"></div>
            </div>
          ))}
        </div>
        
        <div className="mt-16 grid md:grid-cols-2 gap-8">
          <div className="glass-card bg-black/40 border border-purple-500/30 p-6 rounded-lg backdrop-blur-md">
            <h3 className="font-vt323 text-xl text-magenta-400 mb-4">FRONTEND ARSENAL</h3>
            <div className="space-y-2 font-mono text-sm text-gray-300">
              <div>→ React, Vue.js, TypeScript</div>
              <div>→ Tailwind CSS, SCSS, Styled Components</div>
              <div>→ Three.js, GSAP, Framer Motion</div>
            </div>
          </div>
          
          <div className="glass-card bg-black/40 border border-purple-500/30 p-6 rounded-lg backdrop-blur-md">
            <h3 className="font-vt323 text-xl text-cyan-400 mb-4">BACKEND STACK</h3>
            <div className="space-y-2 font-mono text-sm text-gray-300">
              <div>→ Node.js, Python, PostgreSQL</div>
              <div>→ Redis, Docker, Kubernetes</div>
              <div>→ AWS, GCP, Serverless Functions</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Tools;