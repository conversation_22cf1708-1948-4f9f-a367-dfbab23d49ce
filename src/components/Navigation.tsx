import React from 'react';
import { Terminal } from 'lucide-react';

const Navigation: React.FC = () => {
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 bg-black/85 backdrop-blur-md border-b border-subtle">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-14">
          <div className="flex items-center space-x-2">
            <Terminal className="w-5 h-5 text-accent-blue" />
            <button
              onClick={() => scrollToSection('hero')}
              className="font-vt323 text-lg text-primary tracking-wider hover:text-accent-blue transition-colors"
            >
              ALEX_NOVA.exe
            </button>
          </div>

          <div className="hidden md:flex items-center space-x-6">
            {[
              { name: 'TOOLKIT', id: 'tools' },
              { name: 'ABOUT', id: 'about' },
              { name: 'PROJECTS', id: 'projects' },
              { name: 'EXPERIENCE', id: 'experience' },
              { name: 'EDUCATION', id: 'education' },
              { name: 'EXPERTISE', id: 'expertise' }
            ].map((item) => (
              <button
                key={item.name}
                onClick={() => scrollToSection(item.id)}
                className="font-vt323 text-sm text-secondary hover:text-accent-blue transition-colors duration-300 hover:glow"
              >
                {item.name}
              </button>
            ))}
            <button
              onClick={() => scrollToSection('contact')}
              className="bg-magenta-500 hover:bg-magenta-600 text-white px-3 py-1.5 font-vt323 text-sm border border-magenta-400 hover:shadow-neon transition-all duration-300 rounded"
            >
              CONTACT
            </button>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;